{"mcpServers": {"nova-memory": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs"], "timeout": 30000, "env": {"NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data", "NOVA_LOG_LEVEL": "info", "NODE_PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules"}, "autoapprove": ["memory", "board", "workflow", "quick", "relationships", "analysis", "project", "settings", "help"]}}}