#!/usr/bin/env node

// Nova Memory MCP 记忆检索测试脚本
const { spawn } = require('child_process');

// 设置环境变量
process.env.NOVA_MEMORY_PATH = 'G:\\BaiduSyncdisk\\nova-memory-mcp\\data';
process.env.NOVA_LOG_LEVEL = 'info';

console.log('🔍 Nova Memory MCP 记忆检索测试');
console.log('================================');

const mcpServerPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs';

function testMemoryOperations() {
    return new Promise((resolve, reject) => {
        console.log('\n🚀 启动MCP服务器进行记忆操作测试...');
        
        const server = spawn('node', [mcpServerPath], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: process.env
        });

        let responses = [];
        let currentResponse = '';

        server.stdout.on('data', (data) => {
            const output = data.toString();
            currentResponse += output;
            
            // 尝试解析JSON响应
            const lines = currentResponse.split('\n');
            for (let line of lines) {
                if (line.trim()) {
                    try {
                        const response = JSON.parse(line.trim());
                        responses.push(response);
                        console.log(`📨 收到响应 ID ${response.id}:`, JSON.stringify(response, null, 2));
                    } catch (e) {
                        // 不是完整的JSON，继续累积
                    }
                }
            }
        });

        server.stderr.on('data', (data) => {
            console.log('❌ 错误:', data.toString());
        });

        // 1. 初始化
        setTimeout(() => {
            const initRequest = {
                jsonrpc: "2.0",
                id: 1,
                method: "initialize",
                params: {
                    protocolVersion: "2024-11-05",
                    capabilities: { tools: {} },
                    clientInfo: { name: "test-client", version: "1.0.0" }
                }
            };
            server.stdin.write(JSON.stringify(initRequest) + '\n');
        }, 500);

        // 2. 存储测试记忆
        setTimeout(() => {
            const storeRequest = {
                jsonrpc: "2.0",
                id: 2,
                method: "tools/call",
                params: {
                    name: "memory",
                    arguments: {
                        action: "store",
                        content: "Nova Memory MCP测试项目：使用混合部署方式，全局安装npm包，数据存储在G盘自定义目录。支持AI Schema系统的主动行为功能。",
                        tags: ["Nova Memory", "MCP", "测试", "混合部署", "AI Schema"],
                        metadata: {
                            project: "nova-memory-test",
                            type: "configuration",
                            timestamp: new Date().toISOString()
                        }
                    }
                }
            };
            console.log('\n💾 存储测试记忆...');
            server.stdin.write(JSON.stringify(storeRequest) + '\n');
        }, 1500);

        // 3. 搜索记忆
        setTimeout(() => {
            const searchRequest = {
                jsonrpc: "2.0",
                id: 3,
                method: "tools/call",
                params: {
                    name: "memory",
                    arguments: {
                        action: "search",
                        query: "Nova Memory MCP",
                        limit: 5
                    }
                }
            };
            console.log('\n🔍 搜索记忆...');
            server.stdin.write(JSON.stringify(searchRequest) + '\n');
        }, 2500);

        // 4. 检索特定标签
        setTimeout(() => {
            const retrieveRequest = {
                jsonrpc: "2.0",
                id: 4,
                method: "tools/call",
                params: {
                    name: "memory",
                    arguments: {
                        action: "retrieve",
                        tags: ["测试", "混合部署"]
                    }
                }
            };
            console.log('\n📋 按标签检索记忆...');
            server.stdin.write(JSON.stringify(retrieveRequest) + '\n');
        }, 3500);

        // 5. 获取帮助信息
        setTimeout(() => {
            const helpRequest = {
                jsonrpc: "2.0",
                id: 5,
                method: "tools/call",
                params: {
                    name: "help",
                    arguments: {}
                }
            };
            console.log('\n❓ 获取帮助信息...');
            server.stdin.write(JSON.stringify(helpRequest) + '\n');
        }, 4500);

        // 6. 结束测试
        setTimeout(() => {
            server.kill();
            
            console.log('\n🎯 测试完成，收到的响应数量:', responses.length);
            
            // 分析响应
            const initResponse = responses.find(r => r.id === 1);
            const storeResponse = responses.find(r => r.id === 2);
            const searchResponse = responses.find(r => r.id === 3);
            const retrieveResponse = responses.find(r => r.id === 4);
            const helpResponse = responses.find(r => r.id === 5);

            console.log('\n📊 测试结果分析:');
            console.log(`✅ 初始化: ${initResponse ? '成功' : '失败'}`);
            console.log(`✅ 存储记忆: ${storeResponse ? '成功' : '失败'}`);
            console.log(`✅ 搜索记忆: ${searchResponse ? '成功' : '失败'}`);
            console.log(`✅ 检索记忆: ${retrieveResponse ? '成功' : '失败'}`);
            console.log(`✅ 帮助信息: ${helpResponse ? '成功' : '失败'}`);

            resolve({
                success: responses.length >= 4,
                responses: responses,
                details: {
                    init: !!initResponse,
                    store: !!storeResponse,
                    search: !!searchResponse,
                    retrieve: !!retrieveResponse,
                    help: !!helpResponse
                }
            });
        }, 6000);

        server.on('error', (error) => {
            console.log(`❌ 服务器错误: ${error.message}`);
            reject(error);
        });
    });
}

// 运行测试
async function runRetrievalTests() {
    try {
        const result = await testMemoryOperations();
        
        console.log('\n🏆 记忆检索测试总结:');
        console.log(`总体状态: ${result.success ? '✅ 成功' : '❌ 失败'}`);
        console.log(`响应数量: ${result.responses.length}`);
        
        Object.entries(result.details).forEach(([operation, success]) => {
            console.log(`${operation}: ${success ? '✅' : '❌'}`);
        });
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
    }
}

runRetrievalTests();
