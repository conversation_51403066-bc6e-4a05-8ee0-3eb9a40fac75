# Nova Memory MCP 全局安装配置指南

> 轻量本地AI记忆方案 - 简单快速的上下文记忆解决方案

## 📋 概述

Nova Memory是一个100%本地的AI记忆系统，专为Claude和Cursor优化，提供：
- 持久化记忆存储
- 主动行为模式
- 显著的token节省
- 跨AI编程工具支持

## 🚀 全局安装

### 第一步：安装Nova Memory MCP包

```bash
# 全局安装Nova Memory MCP
npm install -g @nova-mcp/mcp-nova

# 验证安装
mcp-nova --version
```

### Windows系统可能需要的构建工具：

```bash
# 如果遇到SQLite编译错误
npm install -g windows-build-tools
```

### macOS系统可能需要：

```bash
# 如果遇到构建错误
xcode-select --install

# 如果npm全局命令无法找到，添加到PATH
echo 'export PATH="$PATH:$(npm bin -g)"' >> ~/.zshrc
source ~/.zshrc
```

### Linux系统可能需要：

```bash
# 安装构建工具
sudo apt-get install build-essential

# 安装Node.js（如果未安装）
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## 🛠️ 配置方法

### 1. Claude Code CLI 全局配置

#### macOS/Linux 系统：
```bash
claude mcp add mcp-nova mcp-nova
```

#### Windows 系统：
```bash
# 需要替换YOUR_USERNAME为实际用户名
claude mcp add mcp-nova node "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\bin\\nova-memory-mcp.mjs"
```

#### 验证Claude Code配置：
```bash
claude mcp list
```

### 2. VSCode 全局MCP配置

#### VSCode 全局设置文件位置：
- **Windows**: `%APPDATA%\Code\User\settings.json`
- **macOS**: `~/Library/Application Support/Code/User/settings.json`  
- **Linux**: `~/.config/Code/User/settings.json`

#### VSCode settings.json 配置：
```json
{
  "mcp.servers": {
    "nova-memory": {
      "command": "mcp-nova",
      "args": [],
      "enabled": true
    }
  }
}
```

#### 替代方案（如果PATH有问题）：
```json
{
  "mcp.servers": {
    "nova-memory": {
      "command": "npx",
      "args": ["@nova-mcp/mcp-nova"],
      "enabled": true
    }
  }
}
```

### 3. Augment 全局MCP配置

#### Augment 配置文件位置：
- **全局配置**: `~/.augment/config.json`
- **项目配置**: `.augment/config.json`

#### Augment config.json 配置：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "mcp-nova",
      "args": [],
      "enabled": true,
      "timeout": 30000
    }
  }
}
```

#### 环境变量方式（推荐）：
```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
export AUGMENT_MCP_SERVERS='{"nova-memory":{"command":"mcp-nova","args":[]}}'
```

## 📋 通用MCP Server JSON格式

### 1. 标准MCP Server配置模板：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "mcp-nova",
      "args": [],
      "timeout": 30000,
      "env": {},
      "autoapprove": ["store_memory", "retrieve_memory", "search_memory"]
    }
  }
}
```

### 2. Windows PATH问题替代方案：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "npx",
      "args": ["@nova-mcp/mcp-nova"],
      "timeout": 30000,
      "env": {},
      "autoapprove": ["store_memory", "retrieve_memory", "search_memory"]
    }
  }
}
```

### 3. 完整路径配置（Windows）：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "node",
      "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\bin\\nova-memory-mcp.mjs"],
      "timeout": 30000,
      "env": {},
      "autoapprove": ["store_memory", "retrieve_memory", "search_memory"]
    }
  }
}
```

### 4. 环境变量增强配置：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "mcp-nova",
      "args": [],
      "timeout": 30000,
      "env": {
        "NOVA_MEMORY_PATH": "${HOME}/.nova-memory",
        "NOVA_LOG_LEVEL": "info"
      },
      "autoapprove": ["store_memory", "retrieve_memory", "search_memory"]
    }
  }
}
```

## ✅ 验证安装和测试功能

### 1. 命令行验证：
```bash
# 检查Nova Memory安装
mcp-nova --version

# 测试基本功能
nova-memory store "测试记忆内容"
nova-memory query "测试"
```

### 2. MCP服务器测试：
```bash
# 启动MCP服务器
nova-memory-mcp

# 应该看到输出：
# "Nova Memory MCP Server running on stdio"
```

### 3. Claude Code验证：
```bash
# 检查MCP服务器列表
claude mcp list

# 应该看到nova-memory或mcp-nova
```

### 4. 完整验证脚本：
```bash
#!/bin/bash
echo "=== Nova Memory 安装验证 ==="

# 检查安装
if command -v mcp-nova &> /dev/null; then
    echo "✅ Nova Memory MCP已安装"
    mcp-nova --version
else
    echo "❌ Nova Memory MCP未安装"
    exit 1
fi

# 测试存储功能
echo "测试存储功能..."
nova-memory store "安装验证测试-$(date)"

# 测试查询功能
echo "测试查询功能..."
nova-memory query "安装验证"

echo "✅ Nova Memory 验证完成"
```

## 🔧 故障排除

### 常见问题解决：

#### 1. PATH 问题
如果命令无法找到，尝试：
```bash
# 添加npm全局bin目录到PATH
export PATH="$PATH:$(npm bin -g)"
```

#### 2. 权限问题（Linux/macOS）
```bash
# 使用sudo安装（不推荐，优先使用nvm）
sudo npm install -g @nova-mcp/mcp-nova
```

#### 3. SQLite编译错误
```bash
# Windows
npm install -g windows-build-tools

# macOS
xcode-select --install

# Linux
sudo apt-get install build-essential
```

#### 4. 验证全局安装位置
```bash
# 查看npm全局包安装位置
npm list -g --depth=0

# 查看特定包位置
npm list -g @nova-mcp/mcp-nova
```

## 📚 Nova Memory 特性

### 核心功能：
- **本地存储**: 所有数据存储在本地SQLite数据库
- **语义搜索**: 智能内容检索和关联
- **跨会话记忆**: 持久化上下文记忆
- **高性能**: 平均响应时间131ms（45K+条目测试）
- **自动优化**: 内置清理和优化功能

### 支持的AI工具：
- Claude Code CLI
- Claude Desktop
- VSCode (通过MCP扩展)
- Cursor IDE
- Augment
- 其他支持MCP协议的AI工具

## 📝 使用说明

安装配置完成后，Nova Memory将：
1. 自动存储与AI的对话上下文
2. 智能关联相关记忆内容
3. 在需要时主动提供相关背景信息
4. 减少重复解释，提高AI响应效率

---

**安装完成后重启相关AI工具以确保配置生效**

*更新时间: 2025-08-15*
*版本: Nova Memory MCP v0.1.2+*