# Nova Memory MCP 环境变量清理指南

> **当前状态**: Nova Memory MCP在Augment中正常工作  
> **数据存储**: 项目隔离模式 (正确)  
> **评估时间**: 2025-08-15

## 📊 环境变量检查结果

### 当前系统环境变量状态
- **NOVA_MEMORY_PATH**: `G:\BaiduSyncdisk\nova-memory-mcp\data` (用户级)
- **NOVA_LOG_LEVEL**: `info` (可能已设置)
- **PATH**: 可能包含 `G:\BaiduSyncdisk\nova-memory-mcp\bin`

### 实际MCP工作状态
- **Augment连接**: ✅ 正常
- **数据存储位置**: `C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\.nova\memory.db`
- **功能测试**: ✅ 所有9个工具正常工作

## 🎯 冲突评估结论

### ✅ 无实际冲突
**重要发现**: 系统环境变量**没有**干扰当前正常工作的MCP配置，原因：

1. **配置优先级**: Augment MCP配置中的 `env` 设置会覆盖系统环境变量
2. **项目隔离**: Nova Memory MCP自动采用项目隔离策略
3. **正确行为**: 数据存储在项目目录是设计预期，不是错误

### 📋 详细分析

#### Augment MCP配置 (优先级最高)
```json
"env": {
  "NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data",
  "NOVA_LOG_LEVEL": "info",
  "NODE_PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules"
}
```

#### 实际运行行为
- MCP读取Augment配置中的环境变量
- 但Nova Memory采用项目隔离策略
- 自动在项目根目录创建 `.nova` 文件夹
- 这是**正确的设计行为**，不是配置错误

## 🤔 是否需要清理？

### 方案A: 保留环境变量 (推荐)
**优点**:
- 不影响当前正常工作的配置
- 为其他环境(VSCode, WSL)提供默认值
- 避免不必要的系统更改

**缺点**:
- 系统中存在未使用的环境变量

### 方案B: 清理环境变量
**优点**:
- 系统更干净
- 避免潜在的混淆

**缺点**:
- 可能影响其他环境的部署
- 需要额外的系统操作

## 🛠️ 清理命令 (如果选择清理)

### 安全清理步骤

#### 1. 备份当前PATH
```cmd
echo %PATH% > path_backup.txt
```

#### 2. 移除NOVA_MEMORY_PATH
```cmd
reg delete "HKCU\Environment" /v NOVA_MEMORY_PATH /f
```

#### 3. 移除NOVA_LOG_LEVEL
```cmd
reg delete "HKCU\Environment" /v NOVA_LOG_LEVEL /f
```

#### 4. 清理PATH中的nova-memory路径
```powershell
# PowerShell方法 (更安全)
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$newPath = ($currentPath -split ';' | Where-Object { $_ -notlike '*nova-memory*' }) -join ';'
[Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
```

#### 5. 刷新环境变量
```cmd
# 重启PowerShell或执行
refreshenv
```

### 验证清理结果
```powershell
# 检查是否已清理
[Environment]::GetEnvironmentVariable("NOVA_MEMORY_PATH", "User")
[Environment]::GetEnvironmentVariable("NOVA_LOG_LEVEL", "User")
[Environment]::GetEnvironmentVariable("PATH", "User") -split ';' | Where-Object { $_ -like '*nova-memory*' }
```

## 🎯 推荐行动方案

### 🥇 方案1: 不清理 (强烈推荐)
**理由**:
- 当前MCP工作完全正常
- 环境变量没有造成实际冲突
- 保留为其他环境提供便利

**行动**: 无需任何操作

### 🥈 方案2: 选择性清理
**如果您坚持清理**，建议只清理PATH：
```powershell
# 只移除PATH中的nova-memory路径
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$newPath = ($currentPath -split ';' | Where-Object { $_ -notlike '*nova-memory*' }) -join ';'
[Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
```

**保留**: NOVA_MEMORY_PATH 和 NOVA_LOG_LEVEL (为其他环境备用)

### 🥉 方案3: 完全清理
只有在确定不会在其他环境部署时才执行完全清理。

## ⚠️ 重要提醒

### 清理前确认
- [ ] Augment中的Nova Memory MCP当前工作正常
- [ ] 理解清理不会影响当前Augment配置
- [ ] 确认不需要在VSCode或WSL中部署

### 清理后验证
- [ ] Augment中的MCP仍然正常工作
- [ ] 数据库文件仍在正确位置
- [ ] 所有功能测试通过

## 📝 结论

**最终建议**: **不需要清理**这些环境变量。

**原因**:
1. 没有造成实际冲突
2. 当前配置工作完美
3. 保留为未来其他环境部署提供便利
4. 遵循"如果没坏，就不要修"的原则

Nova Memory MCP的项目隔离设计确保了即使存在系统环境变量，也会在正确的项目位置存储数据。这是功能特性，不是问题。

---

**评估结果**: ✅ 无需清理，当前配置最优  
**风险等级**: 🟢 低风险 (环境变量无害)  
**推荐行动**: 🚫 无需操作
