@echo off
REM Nova Memory MCP 安装验证脚本
REM 目标路径: G:\BaiduSyncdisk\nova-memory-mcp

echo ================================================
echo Nova Memory MCP 安装验证
echo 目标路径: G:\BaiduSyncdisk\nova-memory-mcp
echo ================================================

set INSTALL_PATH=G:\BaiduSyncdisk\nova-memory-mcp
set ERROR_COUNT=0

REM 检查安装目录
echo 检查安装目录...
if exist "%INSTALL_PATH%" (
    echo ✅ 安装目录存在: %INSTALL_PATH%
) else (
    echo ❌ 安装目录不存在: %INSTALL_PATH%
    set /a ERROR_COUNT+=1
)

REM 检查package.json
echo 检查package.json...
if exist "%INSTALL_PATH%\package.json" (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 不存在
    set /a ERROR_COUNT+=1
)

REM 检查MCP服务器文件
echo 检查MCP服务器文件...
if exist "%INSTALL_PATH%\bin\nova-memory-mcp.mjs" (
    echo ✅ MCP服务器文件存在
) else (
    echo ❌ MCP服务器文件不存在
    set /a ERROR_COUNT+=1
)

REM 检查node_modules
echo 检查依赖包...
if exist "%INSTALL_PATH%\node_modules" (
    echo ✅ node_modules 目录存在
) else (
    echo ❌ node_modules 目录不存在，请运行 npm install
    set /a ERROR_COUNT+=1
)

REM 检查数据目录
echo 检查数据目录...
if not exist "%INSTALL_PATH%\data" (
    echo ⚠️  数据目录不存在，将自动创建
    mkdir "%INSTALL_PATH%\data" 2>nul
    if exist "%INSTALL_PATH%\data" (
        echo ✅ 数据目录已创建
    ) else (
        echo ❌ 无法创建数据目录
        set /a ERROR_COUNT+=1
    )
) else (
    echo ✅ 数据目录存在
)

REM 检查Node.js
echo 检查Node.js环境...
node --version >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo ✅ Node.js 可用:
    node --version
) else (
    echo ❌ Node.js 不可用或未安装
    set /a ERROR_COUNT+=1
)

REM 测试MCP服务器
if %ERROR_COUNT% == 0 (
    echo.
    echo 测试MCP服务器启动...
    cd /d "%INSTALL_PATH%"
    
    REM 设置环境变量
    set NOVA_MEMORY_PATH=%INSTALL_PATH%\data
    set NOVA_LOG_LEVEL=info
    
    echo 启动测试（5秒后自动停止）...
    timeout 5 >nul & taskkill /f /im node.exe >nul 2>&1 &
    node bin\nova-memory-mcp.mjs 2>nul
    
    echo ✅ MCP服务器测试完成
)

echo.
echo ================================================
if %ERROR_COUNT% == 0 (
    echo ✅ 所有检查通过！Nova Memory MCP 安装正确
    echo.
    echo 下一步操作：
    echo 1. 配置Claude Code CLI: 运行配置命令
    echo 2. 配置VSCode: 更新settings.json
    echo 3. 配置Augment: 更新config.json
    echo 4. 使用start-nova-mcp.bat启动服务器
) else (
    echo ❌ 发现 %ERROR_COUNT% 个问题，请检查安装
    echo.
    echo 建议操作：
    echo 1. 检查安装路径是否正确
    echo 2. 重新运行安装命令
    echo 3. 确保Node.js已正确安装
)
echo ================================================

pause