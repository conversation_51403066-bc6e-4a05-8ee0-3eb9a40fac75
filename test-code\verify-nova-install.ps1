# Nova Memory MCP 安装验证脚本 (PowerShell版本)
# 目标路径: G:\BaiduSyncdisk\nova-memory-mcp

param(
    [switch]$TestServer = $false
)

Write-Host "================================================" -ForegroundColor Cyan
Write-Host "Nova Memory MCP 安装验证" -ForegroundColor Cyan
Write-Host "目标路径: G:\BaiduSyncdisk\nova-memory-mcp" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

$installPath = "G:\BaiduSyncdisk\nova-memory-mcp"
$errorCount = 0

# 检查安装目录
Write-Host "`n检查安装目录..." -ForegroundColor Yellow
if (Test-Path $installPath) {
    Write-Host "✅ 安装目录存在: $installPath" -ForegroundColor Green
} else {
    Write-Host "❌ 安装目录不存在: $installPath" -ForegroundColor Red
    $errorCount++
}

# 检查关键文件
$files = @{
    "package.json" = "package.json"
    "MCP服务器文件" = "bin\nova-memory-mcp.mjs"
    "依赖包目录" = "node_modules"
}

foreach ($desc in $files.Keys) {
    $file = $files[$desc]
    $fullPath = Join-Path $installPath $file
    Write-Host "检查 $desc..." -ForegroundColor Yellow
    
    if (Test-Path $fullPath) {
        Write-Host "✅ $desc 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $desc 不存在: $file" -ForegroundColor Red
        $errorCount++
    }
}

# 检查并创建数据目录
Write-Host "检查数据目录..." -ForegroundColor Yellow
$dataPath = Join-Path $installPath "data"
if (-not (Test-Path $dataPath)) {
    Write-Host "⚠️  数据目录不存在，正在创建..." -ForegroundColor Yellow
    try {
        New-Item -Path $dataPath -ItemType Directory -Force | Out-Null
        Write-Host "✅ 数据目录已创建" -ForegroundColor Green
    } catch {
        Write-Host "❌ 无法创建数据目录: $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
} else {
    Write-Host "✅ 数据目录存在" -ForegroundColor Green
}

# 检查Node.js
Write-Host "检查Node.js环境..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "✅ Node.js 可用: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js 命令不可用"
    }
} catch {
    Write-Host "❌ Node.js 不可用或未安装" -ForegroundColor Red
    $errorCount++
}

# 检查npm包信息
if (Test-Path (Join-Path $installPath "package.json")) {
    Write-Host "检查包信息..." -ForegroundColor Yellow
    try {
        $packageInfo = Get-Content (Join-Path $installPath "package.json") | ConvertFrom-Json
        Write-Host "✅ 包名: $($packageInfo.name)" -ForegroundColor Green
        Write-Host "✅ 版本: $($packageInfo.version)" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  无法读取package.json信息" -ForegroundColor Yellow
    }
}

# 测试MCP服务器（可选）
if ($TestServer -and $errorCount -eq 0) {
    Write-Host "`n测试MCP服务器启动..." -ForegroundColor Yellow
    
    # 设置环境变量
    $env:NOVA_MEMORY_PATH = Join-Path $installPath "data"
    $env:NOVA_LOG_LEVEL = "info"
    
    try {
        Set-Location $installPath
        Write-Host "启动测试（3秒后自动停止）..." -ForegroundColor Yellow
        
        $job = Start-Job -ScriptBlock {
            param($path)
            Set-Location $path
            node bin\nova-memory-mcp.mjs
        } -ArgumentList $installPath
        
        Start-Sleep -Seconds 3
        Stop-Job $job -ErrorAction SilentlyContinue
        Remove-Job $job -ErrorAction SilentlyContinue
        
        Write-Host "✅ MCP服务器测试完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ MCP服务器测试失败: $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

# 总结报告
Write-Host "`n================================================" -ForegroundColor Cyan
if ($errorCount -eq 0) {
    Write-Host "✅ 所有检查通过！Nova Memory MCP 安装正确" -ForegroundColor Green
    Write-Host "`n下一步操作：" -ForegroundColor Yellow
    Write-Host "1. 配置Claude Code CLI:" -ForegroundColor White
    Write-Host "   claude mcp add nova-memory node `"$installPath\bin\nova-memory-mcp.mjs`"" -ForegroundColor Gray
    Write-Host "2. 配置VSCode: 更新settings.json" -ForegroundColor White
    Write-Host "3. 配置Augment: 更新config.json" -ForegroundColor White
    Write-Host "4. 使用start-nova-mcp.bat启动服务器" -ForegroundColor White
    
    Write-Host "`n快速启动命令：" -ForegroundColor Yellow
    Write-Host ".\test-code\start-nova-mcp.bat" -ForegroundColor Gray
} else {
    Write-Host "❌ 发现 $errorCount 个问题，请检查安装" -ForegroundColor Red
    Write-Host "`n建议操作：" -ForegroundColor Yellow
    Write-Host "1. 检查安装路径是否正确" -ForegroundColor White
    Write-Host "2. 重新运行安装命令" -ForegroundColor White
    Write-Host "3. 确保Node.js已正确安装" -ForegroundColor White
    Write-Host "4. 运行: npm install 在安装目录中" -ForegroundColor White
}
Write-Host "================================================" -ForegroundColor Cyan

# 显示使用说明
Write-Host "`n使用说明：" -ForegroundColor Yellow
Write-Host "• 重新运行验证: .\verify-nova-install.ps1" -ForegroundColor White
Write-Host "• 包含服务器测试: .\verify-nova-install.ps1 -TestServer" -ForegroundColor White
Write-Host "• 查看详细配置: 参考 nova-memory-mcp-custom-deploy.md" -ForegroundColor White

Read-Host "`n按回车键退出"