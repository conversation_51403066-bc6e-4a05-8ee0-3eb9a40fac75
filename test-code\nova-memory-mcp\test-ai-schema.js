#!/usr/bin/env node

// Nova Memory MCP AI Schema系统测试脚本
const { spawn } = require('child_process');

// 设置环境变量
process.env.NOVA_MEMORY_PATH = 'G:\\BaiduSyncdisk\\nova-memory-mcp\\data';
process.env.NOVA_LOG_LEVEL = 'info';

console.log('🤖 Nova Memory MCP AI Schema系统测试');
console.log('================================');

const mcpServerPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs';

function testAISchema() {
    return new Promise((resolve, reject) => {
        console.log('\n🚀 启动MCP服务器进行AI Schema测试...');
        
        const server = spawn('node', [mcpServerPath], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: process.env
        });

        let responses = [];
        let currentResponse = '';

        server.stdout.on('data', (data) => {
            const output = data.toString();
            currentResponse += output;
            
            // 尝试解析JSON响应
            const lines = currentResponse.split('\n');
            for (let line of lines) {
                if (line.trim()) {
                    try {
                        const response = JSON.parse(line.trim());
                        responses.push(response);
                        console.log(`📨 收到响应 ID ${response.id}`);
                        
                        // 显示详细内容
                        if (response.result && response.result.content) {
                            const content = response.result.content[0];
                            if (content && content.text) {
                                try {
                                    const parsed = JSON.parse(content.text);
                                    if (parsed._ai_result || parsed.results || parsed.content) {
                                        console.log(`   📄 内容预览: ${JSON.stringify(parsed).substring(0, 100)}...`);
                                    }
                                } catch (e) {
                                    console.log(`   📄 文本内容: ${content.text.substring(0, 100)}...`);
                                }
                            }
                        }
                    } catch (e) {
                        // 不是完整的JSON，继续累积
                    }
                }
            }
        });

        server.stderr.on('data', (data) => {
            console.log('❌ 错误:', data.toString());
        });

        const tests = [
            // 1. 初始化
            {
                id: 1,
                name: "初始化",
                request: {
                    jsonrpc: "2.0",
                    id: 1,
                    method: "initialize",
                    params: {
                        protocolVersion: "2024-11-05",
                        capabilities: { tools: {} },
                        clientInfo: { name: "ai-schema-test", version: "1.0.0" }
                    }
                }
            },
            // 2. 获取AI模式帮助
            {
                id: 2,
                name: "AI模式帮助",
                request: {
                    jsonrpc: "2.0",
                    id: 2,
                    method: "tools/call",
                    params: {
                        name: "help",
                        arguments: {
                            ai_mode: true
                        }
                    }
                }
            },
            // 3. 测试静默操作
            {
                id: 3,
                name: "静默存储",
                request: {
                    jsonrpc: "2.0",
                    id: 3,
                    method: "tools/call",
                    params: {
                        name: "memory",
                        arguments: {
                            action: "store",
                            content: "UserService实体：负责用户认证和授权，使用JWT令牌，30分钟过期时间",
                            tags: ["UserService", "认证", "JWT", "实体"],
                            silent: true,
                            metadata: {
                                entity_type: "service",
                                entity_name: "UserService"
                            }
                        }
                    }
                }
            },
            // 4. 测试实体检测
            {
                id: 4,
                name: "实体检测搜索",
                request: {
                    jsonrpc: "2.0",
                    id: 4,
                    method: "tools/call",
                    params: {
                        name: "memory",
                        arguments: {
                            action: "search",
                            query: "UserService",
                            entity_detection: true
                        }
                    }
                }
            },
            // 5. 测试版本追踪
            {
                id: 5,
                name: "版本追踪",
                request: {
                    jsonrpc: "2.0",
                    id: 5,
                    method: "tools/call",
                    params: {
                        name: "memory",
                        arguments: {
                            action: "versions",
                            entity_name: "UserService"
                        }
                    }
                }
            },
            // 6. 测试项目信息
            {
                id: 6,
                name: "项目隔离测试",
                request: {
                    jsonrpc: "2.0",
                    id: 6,
                    method: "tools/call",
                    params: {
                        name: "project",
                        arguments: {
                            action: "info"
                        }
                    }
                }
            },
            // 7. 测试关系映射
            {
                id: 7,
                name: "关系映射",
                request: {
                    jsonrpc: "2.0",
                    id: 7,
                    method: "tools/call",
                    params: {
                        name: "relationships",
                        arguments: {
                            action: "map",
                            entity: "UserService"
                        }
                    }
                }
            },
            // 8. 测试分析功能
            {
                id: 8,
                name: "智能分析",
                request: {
                    jsonrpc: "2.0",
                    id: 8,
                    method: "tools/call",
                    params: {
                        name: "analysis",
                        arguments: {
                            action: "patterns",
                            target: "entities"
                        }
                    }
                }
            }
        ];

        // 执行测试
        let testIndex = 0;
        function runNextTest() {
            if (testIndex < tests.length) {
                const test = tests[testIndex];
                console.log(`\n${testIndex + 1}. 测试 ${test.name}...`);
                server.stdin.write(JSON.stringify(test.request) + '\n');
                testIndex++;
                setTimeout(runNextTest, 1500);
            } else {
                // 所有测试完成
                setTimeout(() => {
                    server.kill();
                    
                    console.log('\n🎯 AI Schema测试完成');
                    console.log(`总响应数量: ${responses.length}`);
                    
                    // 分析结果
                    const results = {};
                    tests.forEach(test => {
                        const response = responses.find(r => r.id === test.id);
                        results[test.name] = {
                            success: !!response && !!response.result,
                            response: response
                        };
                    });

                    resolve({
                        success: Object.values(results).filter(r => r.success).length >= 6,
                        results: results,
                        totalResponses: responses.length
                    });
                }, 2000);
            }
        }

        // 开始测试
        setTimeout(runNextTest, 500);

        server.on('error', (error) => {
            console.log(`❌ 服务器错误: ${error.message}`);
            reject(error);
        });
    });
}

// 运行测试
async function runAISchemaTests() {
    try {
        const result = await testAISchema();
        
        console.log('\n🏆 AI Schema系统测试总结:');
        console.log(`总体状态: ${result.success ? '✅ 成功' : '❌ 失败'}`);
        console.log(`总响应数量: ${result.totalResponses}`);
        
        console.log('\n📊 各功能测试结果:');
        Object.entries(result.results).forEach(([testName, result]) => {
            console.log(`${testName}: ${result.success ? '✅ 成功' : '❌ 失败'}`);
        });
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
    }
}

runAISchemaTests();
