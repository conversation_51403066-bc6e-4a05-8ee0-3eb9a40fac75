# Nova Memory MCP 直接测试指南

> **测试方法**: 直接调用已连接的MCP工具  
> **测试环境**: Augment环境  
> **更新时间**: 2025-08-15

## 📋 正确的测试方法

### ❌ 错误方法
- 使用外部脚本测试MCP服务器
- 通过Node.js直接调用MCP文件
- 绕过Augment环境进行测试

### ✅ 正确方法
- 直接在Augment环境中调用MCP工具
- 使用已连接的Nova Memory工具
- 验证AI助手的自动记忆功能

## 🛠️ 直接MCP测试步骤

### 1. 验证MCP连接
首先确认Nova Memory MCP已在Augment中正确连接：
- 检查MCP服务器列表
- 确认nova-memory工具可用
- 验证环境变量设置

### 2. 记忆存储测试
直接使用memory工具存储测试内容：

```
请使用memory工具存储以下信息：
"Nova Memory MCP测试：混合部署成功，支持AI Schema系统，数据存储在项目隔离目录中"
标签：["测试", "部署", "Nova Memory", "MCP"]
```

### 3. 记忆检索测试
使用memory工具搜索刚才存储的内容：

```
请搜索包含"Nova Memory MCP"的记忆内容
```

### 4. 其他工具测试
依次测试其他8个工具：

#### Help工具测试
```
请使用help工具显示Nova Memory的帮助信息
```

#### Board工具测试
```
请使用board工具创建一个名为"Nova Memory测试任务板"的任务板
```

#### Workflow工具测试
```
请使用workflow工具创建一个测试工作流
```

#### Quick工具测试
```
请使用quick工具快速保存一条测试信息
```

#### Relationships工具测试
```
请使用relationships工具创建Nova Memory MCP与Augment环境的关系
```

#### Analysis工具测试
```
请使用analysis工具分析当前的记忆数据
```

#### Project工具测试
```
请使用project工具显示当前项目信息
```

#### Settings工具测试
```
请使用settings工具显示当前配置
```

## 🔍 验证要点

### 数据存储验证
- 检查 `G:\z-vscode-claudecode\.nova\memory.db` 文件是否存在
- 验证数据库文件大小是否增长
- 确认数据持久化正常

### 功能响应验证
- 每个工具都应返回成功响应
- 记忆搜索应能找到存储的内容
- AI Schema功能应自动激活

### 性能验证
- 响应时间应 < 100ms
- 内存使用合理
- 无错误信息

## 📊 预期结果

### 成功标准
- ✅ 所有9个工具正常响应
- ✅ 记忆存储和检索功能正常
- ✅ 数据库文件正常创建和增长
- ✅ AI助手能自动利用记忆功能

### 失败排查
如果测试失败，检查：
1. Augment是否重启以加载MCP配置
2. MCP配置JSON格式是否正确
3. 环境变量是否正确设置
4. 全局npm包是否正确安装

## 🚀 WSL环境配置

### Claude Code CLI配置 (WSL)
```json
{
  "nova-memory": {
    "command": "node",
    "args": [
      "/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules/@nova-mcp/mcp-nova/nova-memory-mcp.mjs"
    ],
    "timeout": 30000,
    "env": {
      "NOVA_MEMORY_PATH": "/mnt/g/BaiduSyncdisk/nova-memory-mcp/data",
      "NOVA_LOG_LEVEL": "info",
      "NODE_PATH": "/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules"
    }
  }
}
```

### WSL路径说明
- Windows路径: `C:\Users\<USER>\AppData\Roaming\npm\node_modules\@nova-mcp\mcp-nova\nova-memory-mcp.mjs`
- WSL路径: `/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules/@nova-mcp/mcp-nova/nova-memory-mcp.mjs`

- Windows路径: `G:\BaiduSyncdisk\nova-memory-mcp\data`
- WSL路径: `/mnt/g/BaiduSyncdisk/nova-memory-mcp/data`

## 📝 注意事项

1. **重启要求**: 配置MCP后需要重启Augment或Claude Code CLI
2. **路径格式**: WSL中使用 `/mnt/` 前缀访问Windows驱动器
3. **权限问题**: 确保WSL能访问Windows文件系统
4. **环境变量**: WSL和Windows的环境变量可能不同步

---

**文档类型**: 测试指南  
**存放位置**: test-doc/nova-memory-mcp/  
**符合规范**: CLAUDE.md文件结构要求
