#!/usr/bin/env node

// Gemini Agent's Temporary Test Script for Nova Memory MCP (v3 - Robust Parsing)
const { spawn } = require('child_process');

// Environment settings from settings.json
process.env.NOVA_MEMORY_PATH = 'G:\\BaiduSyncdisk\\nova-memory-mcp\\data';
process.env.NOVA_LOG_LEVEL = 'info';
process.env.NODE_PATH = 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules';

console.log('🧪 Gemini Agent Test: Starting Store-and-Search Test (v3)...');
console.log('======================================================');

const mcpServerPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs';

function runStoreAndSearchTest() {
    return new Promise((resolve, reject) => {
        const server = spawn('node', [mcpServerPath], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: process.env
        });

        let fullOutput = '';
        server.stdout.on('data', (data) => {
            fullOutput += data.toString();
        });
        server.stderr.on('data', (data) => {
            console.error(`MCP Server stderr: ${data}`);
        });

        const testContent = 'Gemini Agent Test: The quick brown fox jumps over the lazy dog.';
        const testQuery = 'Gemini Agent Test';

        // 1. Initialize
        setTimeout(() => {
            const initRequest = {
                jsonrpc: "2.0", id: 1, method: "initialize",
                params: { protocolVersion: "2024-11-05", capabilities: { tools: {} }, clientInfo: { name: "gemini-test-client", version: "1.0" } }
            };
            server.stdin.write(JSON.stringify(initRequest) + '\n');
        }, 500);

        // 2. Store Memory
        setTimeout(() => {
            console.log(`\n1. Storing memory: "${testContent}"`);
            const storeRequest = {
                jsonrpc: "2.0", id: 2, method: "tools/call",
                params: { name: "memory", arguments: { action: "store", content: testContent, tags: ["gemini-test"] } }
            };
            server.stdin.write(JSON.stringify(storeRequest) + '\n');
        }, 1500);

        // 3. Search Memory
        setTimeout(() => {
            console.log(`\n2. Searching for: "${testQuery}"`);
            const searchRequest = {
                jsonrpc: "2.0", id: 3, method: "tools/call",
                params: { name: "memory", arguments: { action: "search", query: testQuery, limit: 1 } }
            };
            server.stdin.write(JSON.stringify(searchRequest) + '\n');
        }, 2500);

        // 4. End Test & Analyze
        setTimeout(() => {
            server.kill();
            console.log('\n3. Test finished. Analyzing results...');
            console.log('======================================================');
            
            try {
                // Robust parsing: split by lines and try to parse each line
                const responses = [];
                const lines = fullOutput.split(/\r?\n/);
                for (const line of lines) {
                    try {
                        const jsonObj = JSON.parse(line);
                        if (jsonObj.jsonrpc === "2.0") {
                            responses.push(jsonObj);
                        }
                    } catch (e) {
                        // Not a JSON line, ignore
                    }
                }

                const storeResponse = responses.find(r => r.id === 2);
                const searchResponse = responses.find(r => r.id === 3);

                let storeSuccess = false;
                if (storeResponse && storeResponse.result && storeResponse.result.content) {
                    const storeResultText = storeResponse.result.content[0].text;
                    const storeResultJson = JSON.parse(storeResultText);
                    if (storeResultJson._ai_result && storeResultJson._ai_result.success) {
                        storeSuccess = true;
                    }
                }

                let searchSuccess = false;
                let foundContent = '';
                if (searchResponse && searchResponse.result && searchResponse.result.content) {
                    const searchResultText = searchResponse.result.content[0].text;
                    const searchResultJson = JSON.parse(searchResultText);
                    if (searchResultJson.results && searchResultJson.results.length > 0) {
                        foundContent = searchResultJson.results[0].content;
                        if (foundContent === testContent) {
                            searchSuccess = true;
                        }
                    }
                }

                console.log(`- Store Operation Success: ${storeSuccess ? '✅' : '❌'}`);
                console.log(`- Search Operation Success: ${searchSuccess ? '✅' : '❌'}`);
                if(searchSuccess) {
                    console.log(`- Found Content: "${foundContent}"`);
                } else {
                    console.log(`- Expected Content: "${testContent}"`);
                    console.log(`- Actually Found: "${foundContent}"`);
                }
                
                resolve(storeSuccess && searchSuccess);

            } catch (e) {
                console.error("Error during analysis:", e);
                console.log("Full MCP Output:\n", fullOutput);
                reject(e);
            }
        }, 4500); // Increased timeout for analysis

        server.on('error', (err) => reject(err));
    });
}

runStoreAndSearchTest()
    .then(success => {
        console.log(`\nFINAL RESULT: ${success ? '✅ Test Passed' : '❌ Test Failed'}`);
        process.exit(success ? 0 : 1);
    })
    .catch(err => {
        console.error('\nError during test execution:', err);
        process.exit(1);
    });
