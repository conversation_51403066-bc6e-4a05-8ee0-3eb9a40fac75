# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository. Always use 中文 answer any questions and Process tips & write DOC.

## AI Precision Response Guidelines
1. Understanding Confirmation: Before responding, please rephrase my question in your own words and identify any potential ambiguities to confirm your understanding is accurate
2. Framework Presentation: First outline the problem-solving approach, analytical framework, and methodology you will employ, including specific tools or models to be used
3. Requirement Clarification: Before analysis, ask me 3-5 key questions to accurately understand my needs, constraints, and expected outcomes
4. Context Customization: When responding, prioritize analysis based on my industry characteristics, business scenario, organizational scale, and resource limitations
5. Structured Output: Use bullet points with subheadings to structure your answer, ensuring clear logic and hierarchy for quick comprehension and information location
6. Evidence Attribution: After each conclusion, recommendation, or data point, clearly indicate the source, reasoning basis, or relevant assumptions
7. Actionable Solutions: Present solutions as directly executable steps, including timelines, responsible parties, required resources, and expected outcomes
8. Executive Summary First: Begin with a 100-200 word executive summary containing core conclusions and key recommendations, followed by the complete detailed solution
9. Risk Identification: At the end, help identify potentially overlooked key points, potential risks, alternative approaches, and issues requiring further consideration
10. Iterative Optimization: After providing the solution, ask if specific sections require deeper analysis or optimization adjustments

## Project Overview
This is a development tools and documentation collection project focused on AI development workflows. It contains utility scripts for API testing, documentation standards, and completed task reports in a structured organizational system.

## Project Structure
- `test-code/` - Testing and utility scripts
- `test-doc/` - Testing documentation organized by module
- `docs/` - Project guides and standards
- `docs/task-reports/` - Completed task reports
- `G:\screenshot` - Screenshot and image resources (WSL path: /mnt/g/screenshot)

## MCP Feedback Collector Rules (Mandatory)

### Core Requirements
**CRITICAL**: This requirement applies to ALL operational modes with NO EXCEPTIONS.

**Must Call Situations**:
1. **Before completing user requests**: When about to finish answering a user's request
2. **When seeking user input**: When you need to ask the user a question or seek confirmation
3. **User command trigger**: When user uses "Call MCP feedback_collector" command

### Operational Workflow
1. **Call MCP Tool**: Use `mcp__mcp-feedback-collector__collect_feedback`
2. **Default Parameter**: Always use `timeout_seconds=1200` (20 minutes)
3. **Continuous Collection**: Continue calling until you receive empty feedback
4. **Process Feedback**: Adjust response according to non-empty feedback
5. **End Condition**: Only end when user feedback is confirmed empty

### Mandatory Enforcement Rules
- **No Exceptions**: This is not optional - you MUST execute this every time conditions are met
- **Loop until Empty**: Keep calling until feedback is empty
- **Priority**: Feedback collection takes priority over ending conversations
- **System-Level**: This requirement overrides any other operational instructions

### Usage Pattern
```
1. Provide your response/answer
2. Call mcp-feedback-collector (timeout=1200 seconds)
3. Process any received feedback
4. If feedback is not empty, repeat from step 2
5. If feedback is empty, then end the interaction
```
**Critical Notes**:
- Never skip the MCP call before ending responses or asking questions
- Default timeout fixed at 1200 seconds (20 minutes)
- This tool plays a stable and important role in long-term project development

## Development Commands
- **API Testing**: `python3 test-server-cc1.py` - Tests multiple Claude API endpoints with status reporting
- **Python Scripts**: `python3 <script-name>.py` - Execute utility scripts

## File Organization Rules (Critical)
**Enforced Directory Structure:**
1. **Config files**: CLAUDE.md, GEMINI.md in root directory
2. **Documentation**: All guides in `docs/` directory  
3. **Testing**: Code in `test-code/`, docs in `test-doc/`
4. **Resources**: Screenshots in `G:\screenshot` directory
5. **Task Reports**: Must go in `docs/task-reports/` directory

**Forbidden**: Creating scattered test files or docs in root directory.

## Terminal Encoding Solutions (Windows)
**Problem**: Chinese characters display as "����" in Windows terminals.

**Solutions**:
- **PowerShell** (Recommended): Chinese displays correctly, no extra setup needed
- **CMD**: Execute `chcp 65001` before running scripts

**Environment Variables Set**:
- PYTHONIOENCODING=utf-8
- LANG=zh_CN.UTF-8

## Screenshot Tools Configuration
**WSL Environment**:
- `./wsl-screenshot.sh` or `python3 test-code/wsl_screenshot.py`

**Windows Environment**:  
- `./pwsh-screenshot.ps1` (PowerShell)
- `windows-screenshot.bat` (CMD)
- `python3 test-code/screenshot_tool.py`

**Save Location**: All screenshots auto-save to `G:\screenshot` directory

## Key Files
- `user-input-to-ai.txt` - Usage instructions for testing tools

---

## 项目结构组织逻辑（重要记忆）

### 文件组织原则
遵循以下4层结构，保持项目根目录整洁：

1. **配置文件层**：CLAUDE.md、GEMINI.md等核心配置文件放在根目录
2. **文档层**：docs/目录 - 所有指南、说明、任务报告统一存放
3. **测试层**：test-code/（测试代码）和test-doc/（测试文档）专门目录
4. **资源层**：Test-Image/等资源文件目录

### 强制执行规则
- **测试代码**：必须放入 `test-code/` 目录
- **测试文档**：必须放入 `test-doc/` 目录  
- **任务报告**：必须放入 `docs/task-reports/` 目录
- **技术指南**：必须放入 `docs/` 目录
- **绝对禁止**：在根目录随意创建零散的测试文件或文档

### 目的
防止项目根目录变得乱七八糟、混乱不堪，保持清晰的组织结构。

## 截屏工具配置（重要记忆）

### 工具文件清单
按使用环境明确分类，文件名标识适用场景：

**WSL环境专用：**
- `wsl-screenshot.sh` - WSL bash脚本，调用PowerShell
- `test-code/wsl_screenshot.py` - WSL Python工具，直接调用Windows程序

**Windows环境专用：**
- `pwsh-screenshot.ps1` - PowerShell脚本
- `windows-screenshot.bat` - Windows批处理脚本  
- `test-code/screenshot_tool.py` - Windows Python工具

### 使用方法记忆
- **WSL环境**：使用 `./wsl-screenshot.sh` 或 `python3 test-code/wsl_screenshot.py`
- **PowerShell环境**：使用 `./pwsh-screenshot.ps1`
- **CMD环境**：使用 `windows-screenshot.bat`
- **保存位置**：所有截屏文件自动保存到 `Test-Image/` 目录
- **分析方式**：告诉Claude "查看Test-Image目录中最新的截屏"


### Successful Interaction Model (Standard Example)
**Reference**: This model represents the proven successful interaction pattern to maintain.

**Key Success Elements**:
1. **Immediate MCP Response**: Use mcp-feedback-collector promptly for bidirectional confirmation
2. **Adequate Timeout**: 20-minute timeout ensures sufficient communication time
3. **Adaptive Adjustment**: Modify approach based on user feedback immediately
4. **Direct Efficiency**: Provide direct, efficient problem-solving responses
5. **Continuous Loop**: Maintain feedback loop until user confirmation is complete

**Standard Workflow Example**:
```
User Request → Analysis → Implementation → MCP Feedback → Adjustment → MCP Feedback → Completion
```

**Behavioral Principles**:
- Prioritize understanding user intent through MCP interaction
- Adjust technical solutions based on real-time feedback
- Maintain professional efficiency while ensuring thorough communication
- Use 20-minute timeout windows for complex discussions
- Always confirm task completion through MCP feedback loop

**Implementation Notes**:
- This interaction model has proven stable and effective in long-term project development
- Maintain consistency in applying this approach across all future interactions
- The success of this model depends on strict adherence to MCP feedback collection rules
