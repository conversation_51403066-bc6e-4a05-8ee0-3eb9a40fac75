# Nova Memory MCP 自定义路径部署指南

> 部署目标路径：`G:\BaiduSyncdisk\nova-memory-mcp`

## 📋 重要说明

**安装确认**: 我作为AI助手无法直接在你的系统上执行安装命令，你需要按照以下步骤手动执行安装和配置。

## 🚀 自定义路径部署方案

### 第一步：创建目录并下载源码

```bash
# Windows PowerShell 或 CMD
mkdir "G:\BaiduSyncdisk\nova-memory-mcp"
cd "G:\BaiduSyncdisk\nova-memory-mcp"

# 方法1: 从源码安装（推荐）
git clone https://github.com/jagdeepsinghdev/nova-memory.git .
npm install

# 方法2: 从npm包下载到指定目录
npm install @nova-mcp/mcp-nova
```

### 第二步：WSL路径确认
- **Windows路径**: `G:\BaiduSyncdisk\nova-memory-mcp`
- **WSL路径**: `/mnt/g/BaiduSyncdisk/nova-memory-mcp`

## 🛠️ 自定义路径配置方法

### 1. Claude Code CLI 配置（自定义路径）

#### Windows 系统：
```bash
# 使用完整路径指向自定义安装位置
claude mcp add nova-memory node "G:\BaiduSyncdisk\nova-memory-mcp\bin\nova-memory-mcp.mjs"

# 或者指向主要可执行文件
claude mcp add nova-memory node "G:\BaiduSyncdisk\nova-memory-mcp\index.js"
```

#### WSL 系统：
```bash
# WSL中使用Unix路径格式
claude mcp add nova-memory node "/mnt/g/BaiduSyncdisk/nova-memory-mcp/bin/nova-memory-mcp.mjs"
```

### 2. VSCode 全局配置（自定义路径）

#### VSCode settings.json 配置：
```json
{
  "mcp.servers": {
    "nova-memory": {
      "command": "node",
      "args": ["G:\\BaiduSyncdisk\\nova-memory-mcp\\bin\\nova-memory-mcp.mjs"],
      "enabled": true,
      "timeout": 30000,
      "env": {
        "NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data"
      }
    }
  }
}
```


#### WSL用户的VSCode配置：
```json
{
  "mcp.servers": {
    "nova-memory": {
      "command": "node",
      "args": ["/mnt/g/BaiduSyncdisk/nova-memory-mcp/bin/nova-memory-mcp.mjs"],
      "enabled": true,
      "timeout": 30000,
      "env": {
        "NOVA_MEMORY_PATH": "/mnt/g/BaiduSyncdisk/nova-memory-mcp/data"
      }
    }
  }
}
```

### 3. Augment 自定义配置

#### Augment config.json：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "node",
      "args": ["G:\\BaiduSyncdisk\\nova-memory-mcp\\bin\\nova-memory-mcp.mjs"],
      "enabled": true,
      "timeout": 30000,
      "env": {
        "NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data",
        "NOVA_LOG_LEVEL": "info"
      }
    }
  }
}
```

## 📋 通用MCP Server配置（自定义路径）

### 1. 标准自定义路径配置：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "node",
      "args": ["G:\\BaiduSyncdisk\\nova-memory-mcp\\bin\\nova-memory-mcp.mjs"],
      "timeout": 30000,
      "env": {
        "NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data",
        "NOVA_LOG_LEVEL": "info",
        "NODE_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\node_modules"
      },
      "autoapprove": ["store_memory", "retrieve_memory", "search_memory"]
    }
  }
}
```

### 2. PowerShell包装脚本配置：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "powershell",
      "args": [
        "-Command",
        "cd 'G:\\BaiduSyncdisk\\nova-memory-mcp'; node bin\\nova-memory-mcp.mjs"
      ],
      "timeout": 30000,
      "env": {},
      "autoapprove": ["store_memory", "retrieve_memory", "search_memory"]
    }
  }
}
```

### 3. 批处理脚本配置：
首先创建批处理文件 `G:\BaiduSyncdisk\nova-memory-mcp\start-mcp.bat`：
```batch
@echo off
cd /d "G:\BaiduSyncdisk\nova-memory-mcp"
node bin\nova-memory-mcp.mjs %*
```

然后在MCP配置中使用：
```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "G:\\BaiduSyncdisk\\nova-memory-mcp\\start-mcp.bat",
      "args": [],
      "timeout": 30000,
      "env": {},
      "autoapprove": ["store_memory", "retrieve_memory", "search_memory"]
    }
  }
}
```

## 🔧 环境变量配置

wo

### PowerShell Profile 配置：
在 `$PROFILE` 文件中添加：
```powershell
# Nova Memory 环境变量
$env:NOVA_MEMORY_PATH = "G:\BaiduSyncdisk\nova-memory-mcp\data"
$env:NOVA_LOG_LEVEL = "info"
$env:PATH += ";G:\BaiduSyncdisk\nova-memory-mcp\bin"
```

### WSL 环境变量：
在 `~/.bashrc` 或 `~/.zshrc` 中添加：
```bash
# Nova Memory 环境变量
export NOVA_MEMORY_PATH="/mnt/g/BaiduSyncdisk/nova-memory-mcp/data"
export NOVA_LOG_LEVEL="info"
export PATH="$PATH:/mnt/g/BaiduSyncdisk/nova-memory-mcp/bin"
```

## ✅ 验证脚本

### Windows 验证脚本 (`verify-custom-install.bat`)：
```batch
@echo off
echo === Nova Memory 自定义路径安装验证 ===

echo 检查安装目录...
if exist "G:\BaiduSyncdisk\nova-memory-mcp" (
    echo ✅ 安装目录存在
) else (
    echo ❌ 安装目录不存在
    exit /b 1
)

echo 检查主要文件...
if exist "G:\BaiduSyncdisk\nova-memory-mcp\package.json" (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 不存在
)

if exist "G:\BaiduSyncdisk\nova-memory-mcp\bin\nova-memory-mcp.mjs" (
    echo ✅ MCP服务器文件存在
) else (
    echo ❌ MCP服务器文件不存在
)

echo 测试Node.js执行...
cd /d "G:\BaiduSyncdisk\nova-memory-mcp"
node --version

echo 测试MCP服务器启动...
timeout 5 node bin\nova-memory-mcp.mjs

echo ✅ 验证完成
```

### PowerShell 验证脚本 (`verify-custom-install.ps1`)：
```powershell
Write-Host "=== Nova Memory 自定义路径安装验证 ==="

$installPath = "G:\BaiduSyncdisk\nova-memory-mcp"

# 检查安装目录
if (Test-Path $installPath) {
    Write-Host "✅ 安装目录存在" -ForegroundColor Green
} else {
    Write-Host "❌ 安装目录不存在" -ForegroundColor Red
    exit 1
}

# 检查关键文件
$files = @(
    "package.json",
    "bin\nova-memory-mcp.mjs",
    "node_modules"
)

foreach ($file in $files) {
    $fullPath = Join-Path $installPath $file
    if (Test-Path $fullPath) {
        Write-Host "✅ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $file 不存在" -ForegroundColor Red
    }
}

# 测试执行
Set-Location $installPath
Write-Host "当前Node.js版本："
node --version

Write-Host "测试MCP服务器..."
Start-Process -FilePath "node" -ArgumentList "bin\nova-memory-mcp.mjs" -NoNewWindow -Wait -TimeoutSeconds 5

Write-Host "✅ 验证完成" -ForegroundColor Green
```

## 📁 目录结构

安装完成后，目录结构应该如下：
```
G:\BaiduSyncdisk\nova-memory-mcp\
├── bin\
│   └── nova-memory-mcp.mjs
├── data\                    # 记忆数据存储目录
├── node_modules\            # 依赖包
├── package.json
├── start-mcp.bat           # 启动脚本
├── verify-custom-install.bat
└── verify-custom-install.ps1
```

## 🔧 故障排除

### 1. 路径问题
```bash
# 确保路径中没有特殊字符
# 使用双引号包围包含空格的路径
# Windows路径使用双反斜杠 \\ 或正斜杠 /
```

### 2. 权限问题
```cmd
# 以管理员身份运行PowerShell或CMD
# 确保对目标目录有写权限
```

### 3. Node.js路径问题
```bash
# 确保Node.js在系统PATH中
where node
# 或使用完整路径
"C:\Program Files\nodejs\node.exe"
```

### 4. 依赖安装问题
```bash
cd "G:\BaiduSyncdisk\nova-memory-mcp"
npm install --force
npm audit fix
```

## 📝 使用说明

1. **首次运行前**：执行验证脚本确保安装正确
2. **数据存储**：记忆数据将存储在 `G:\BaiduSyncdisk\nova-memory-mcp\data\` 目录
3. **备份重要**：定期备份 data 目录以防数据丢失
4. **更新升级**：直接在安装目录执行 `git pull` 或重新下载

---

**安装完成后记得重启相关AI工具以确保配置生效**

*部署路径: G:\BaiduSyncdisk\nova-memory-mcp*
*更新时间: 2025-08-15*
