@echo off
REM Nova Memory MCP 启动脚本
REM 目标路径: G:\BaiduSyncdisk\nova-memory-mcp

echo ================================================
echo Nova Memory MCP 服务器启动
echo 路径: G:\BaiduSyncdisk\nova-memory-mcp
echo ================================================

REM 检查目录是否存在
if not exist "G:\BaiduSyncdisk\nova-memory-mcp" (
    echo ❌ 错误: 安装目录不存在
    echo 请先安装Nova Memory到指定路径
    pause
    exit /b 1
)

REM 切换到安装目录
cd /d "G:\BaiduSyncdisk\nova-memory-mcp"

REM 检查关键文件
if not exist "bin\nova-memory-mcp.mjs" (
    echo ❌ 错误: MCP服务器文件不存在
    echo 请检查安装是否完整
    pause
    exit /b 1
)

REM 设置环境变量
set NOVA_MEMORY_PATH=G:\BaiduSyncdisk\nova-memory-mcp\data
set NOVA_LOG_LEVEL=info

echo ✅ 启动Nova Memory MCP服务器...
echo 数据目录: %NOVA_MEMORY_PATH%
echo 日志级别: %NOVA_LOG_LEVEL%
echo.

REM 启动MCP服务器
node bin\nova-memory-mcp.mjs

echo.
echo MCP服务器已停止
pause