{
    // Claude Code项目配置
    "claude-code.enableAutoStart": true,

    // HTTP代理设置 - 避免schema获取错误
    "http.proxy": "",
    "http.proxySupport": "off",

    // 编辑器配置
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,

    // 文件排除
    "files.exclude": {
        "**/node_modules": true,
        "**/.git": true,
        "**/dist": true,
        "**/build": true
    },

    // 搜索排除
    "search.exclude": {
        "**/node_modules": true,
        "**/dist": true,
        "**/build": true,
        "**/*.log": true
    },

    // 终端配置（确保不硬编码路径）
    "terminal.integrated.cwd": null,
    "claudeCodeChat.permissions.yoloMode": false,

    "mcp.servers": {
    "nova-memory": {
      "command": "node",
      "args": [
        "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs"
      ],
      "timeout": 30000,
      "enabled": true,
      "env": {
        "NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data",
        "NOVA_LOG_LEVEL": "info",
        "NODE_PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules"
      },
      "autoapprove": [
        "memory",
        "board",
        "workflow",
        "quick",
        "relationships",
        "analysis",
        "project",
        "settings",
        "help"
      ]
    }
  }
}
