#!/usr/bin/env pwsh
# Nova Memory MCP 混合部署验证脚本

Write-Host "=== Nova Memory MCP 混合部署验证 ===" -ForegroundColor Cyan

# 1. 检查环境变量
Write-Host "`n1. 检查环境变量..." -ForegroundColor Yellow
Write-Host "NOVA_MEMORY_PATH: $env:NOVA_MEMORY_PATH" -ForegroundColor Green
Write-Host "NOVA_LOG_LEVEL: $env:NOVA_LOG_LEVEL" -ForegroundColor Green

# 2. 检查全局安装
Write-Host "`n2. 检查全局安装..." -ForegroundColor Yellow
$globalPath = "C:\Users\<USER>\AppData\Roaming\npm\node_modules\@nova-mcp\mcp-nova"
if (Test-Path $globalPath) {
    Write-Host "✅ 全局安装路径存在: $globalPath" -ForegroundColor Green
    Write-Host "主要文件:" -ForegroundColor White
    Get-ChildItem $globalPath -Name | Where-Object { $_ -match "\.(mjs|js|json)$" } | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ 全局安装路径不存在" -ForegroundColor Red
}

# 3. 检查自定义数据目录
Write-Host "`n3. 检查自定义数据目录..." -ForegroundColor Yellow
$dataPath = "G:\BaiduSyncdisk\nova-memory-mcp"
if (Test-Path $dataPath) {
    Write-Host "✅ 数据目录存在: $dataPath" -ForegroundColor Green
    Write-Host "目录结构:" -ForegroundColor White
    Get-ChildItem $dataPath | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Gray }
} else {
    Write-Host "❌ 数据目录不存在" -ForegroundColor Red
}

# 4. 检查PATH配置
Write-Host "`n4. 检查PATH配置..." -ForegroundColor Yellow
if ($env:PATH -like "*nova-memory-mcp\bin*") {
    Write-Host "✅ PATH中包含nova-memory-mcp\bin" -ForegroundColor Green
} else {
    Write-Host "❌ PATH中未包含nova-memory-mcp\bin" -ForegroundColor Red
}

# 5. 测试MCP服务器启动
Write-Host "`n5. 测试MCP服务器启动..." -ForegroundColor Yellow
$env:NOVA_MEMORY_PATH = "G:\BaiduSyncdisk\nova-memory-mcp\data"
$env:NOVA_LOG_LEVEL = "info"

try {
    $process = Start-Process -FilePath "node" -ArgumentList "$globalPath\nova-memory-mcp.mjs" -NoNewWindow -PassThru
    Start-Sleep -Seconds 2
    if (!$process.HasExited) {
        Write-Host "✅ MCP服务器启动成功" -ForegroundColor Green
        $process.Kill()
    } else {
        Write-Host "❌ MCP服务器启动失败" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ MCP服务器启动异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 检查配置文件
Write-Host "`n6. 检查配置文件..." -ForegroundColor Yellow
$configFiles = @(
    "G:\BaiduSyncdisk\nova-memory-mcp\nova-memory.config.js",
    "G:\z-vscode-claudecode\nova-memory-augment-config.json"
)

foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        Write-Host "✅ 配置文件存在: $configFile" -ForegroundColor Green
    } else {
        Write-Host "❌ 配置文件不存在: $configFile" -ForegroundColor Red
    }
}

Write-Host "`n=== 验证完成 ===" -ForegroundColor Cyan
Write-Host "如果所有项目都显示✅，说明Nova Memory MCP混合部署成功！" -ForegroundColor Green
