const fs = require('fs');
const path = require('path');
const assert = require('assert');

// --- Test Configuration ---
const mcpConfigPath = path.join(__dirname, '..', '.mcp.json');
const expectedConfig = {
  "type": "stdio",
  "command": "node",
  "args": [
    "/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules/@nova-mcp/mcp-nova/nova-memory-mcp.mjs"
  ],
  "timeout": 30000,
  "enabled": true,
  "env": {
    "NOVA_MEMORY_PATH": "/mnt/g/BaiduSyncdisk/nova-memory-mcp/data",
    "NOVA_LOG_LEVEL": "info",
    "NODE_PATH": "/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules"
  },
  "autoapprove": [
    "memory",
    "board",
    "workflow",
    "quick",
    "relationships",
    "analysis",
    "project",
    "settings",
    "help"
  ]
};


// --- Test Runner ---
try {
  console.log(`[INFO] Reading config file from: ${mcpConfigPath}`);
  const mcpConfigFile = fs.readFileSync(mcpConfigPath, 'utf8');
  const mcpConfig = JSON.parse(mcpConfigFile);

  console.log('[INFO] Locating "nova-memory" configuration...');
  const novaMemoryConfig = mcpConfig.mcpServers['nova-memory'];

  assert.ok(novaMemoryConfig, '"nova-memory" configuration not found in .mcp.json');
  console.log('[SUCCESS] Found "nova-memory" configuration.');

  console.log('[INFO] Validating configuration details...');
  assert.deepStrictEqual(novaMemoryConfig, expectedConfig, 'The "nova-memory" configuration does not match the expected values.');
  
  console.log('--------------------------------------------------');
  console.log('[SUCCESS] All tests passed!');
  console.log('The "nova-memory" configuration in .mcp.json is valid.');
  console.log('--------------------------------------------------');
  
  process.exit(0);

} catch (error) {
  console.error('--------------------------------------------------');
  console.error('[FAILURE] Test failed!');
  if (error instanceof assert.AssertionError) {
    console.error('Assertion Error:', error.message);
    console.error('Expected:', JSON.stringify(error.expected, null, 2));
    console.error('Actual:', JSON.stringify(error.actual, null, 2));
  } else {
    console.error('An unexpected error occurred:', error);
  }
  console.error('--------------------------------------------------');
  process.exit(1);
}