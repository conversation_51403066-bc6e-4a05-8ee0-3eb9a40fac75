#!/usr/bin/env node

// Nova Memory MCP 全工具功能测试脚本
const { spawn } = require('child_process');

// 设置环境变量
process.env.NOVA_MEMORY_PATH = 'G:\\BaiduSyncdisk\\nova-memory-mcp\\data';
process.env.NOVA_LOG_LEVEL = 'info';

console.log('🛠️ Nova Memory MCP 全工具功能测试');
console.log('================================');

const mcpServerPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs';

function testAllTools() {
    return new Promise((resolve, reject) => {
        console.log('\n🚀 启动MCP服务器进行全工具测试...');
        
        const server = spawn('node', [mcpServerPath], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: process.env
        });

        let responses = [];
        let currentResponse = '';

        server.stdout.on('data', (data) => {
            const output = data.toString();
            currentResponse += output;
            
            // 尝试解析JSON响应
            const lines = currentResponse.split('\n');
            for (let line of lines) {
                if (line.trim()) {
                    try {
                        const response = JSON.parse(line.trim());
                        responses.push(response);
                        console.log(`📨 收到响应 ID ${response.id}: ${response.result ? '✅ 成功' : '❌ 失败'}`);
                    } catch (e) {
                        // 不是完整的JSON，继续累积
                    }
                }
            }
        });

        server.stderr.on('data', (data) => {
            console.log('❌ 错误:', data.toString());
        });

        const tests = [
            // 1. 初始化
            {
                id: 1,
                name: "初始化",
                request: {
                    jsonrpc: "2.0",
                    id: 1,
                    method: "initialize",
                    params: {
                        protocolVersion: "2024-11-05",
                        capabilities: { tools: {} },
                        clientInfo: { name: "test-client", version: "1.0.0" }
                    }
                }
            },
            // 2. Help工具
            {
                id: 2,
                name: "Help工具",
                request: {
                    jsonrpc: "2.0",
                    id: 2,
                    method: "tools/call",
                    params: {
                        name: "help",
                        arguments: {}
                    }
                }
            },
            // 3. Memory工具 - 统计
            {
                id: 3,
                name: "Memory统计",
                request: {
                    jsonrpc: "2.0",
                    id: 3,
                    method: "tools/call",
                    params: {
                        name: "memory",
                        arguments: {
                            action: "stats"
                        }
                    }
                }
            },
            // 4. Board工具
            {
                id: 4,
                name: "Board工具",
                request: {
                    jsonrpc: "2.0",
                    id: 4,
                    method: "tools/call",
                    params: {
                        name: "board",
                        arguments: {
                            action: "create",
                            title: "Nova Memory测试任务板",
                            description: "测试任务管理功能"
                        }
                    }
                }
            },
            // 5. Workflow工具
            {
                id: 5,
                name: "Workflow工具",
                request: {
                    jsonrpc: "2.0",
                    id: 5,
                    method: "tools/call",
                    params: {
                        name: "workflow",
                        arguments: {
                            action: "create",
                            name: "测试工作流",
                            description: "Nova Memory MCP测试工作流程"
                        }
                    }
                }
            },
            // 6. Quick工具
            {
                id: 6,
                name: "Quick工具",
                request: {
                    jsonrpc: "2.0",
                    id: 6,
                    method: "tools/call",
                    params: {
                        name: "quick",
                        arguments: {
                            action: "save",
                            content: "快速保存测试内容"
                        }
                    }
                }
            },
            // 7. Relationships工具
            {
                id: 7,
                name: "Relationships工具",
                request: {
                    jsonrpc: "2.0",
                    id: 7,
                    method: "tools/call",
                    params: {
                        name: "relationships",
                        arguments: {
                            action: "create",
                            from: "Nova Memory MCP",
                            to: "Augment环境",
                            type: "集成关系"
                        }
                    }
                }
            },
            // 8. Analysis工具
            {
                id: 8,
                name: "Analysis工具",
                request: {
                    jsonrpc: "2.0",
                    id: 8,
                    method: "tools/call",
                    params: {
                        name: "analysis",
                        arguments: {
                            action: "analyze",
                            target: "memory"
                        }
                    }
                }
            },
            // 9. Project工具
            {
                id: 9,
                name: "Project工具",
                request: {
                    jsonrpc: "2.0",
                    id: 9,
                    method: "tools/call",
                    params: {
                        name: "project",
                        arguments: {
                            action: "info"
                        }
                    }
                }
            },
            // 10. Settings工具
            {
                id: 10,
                name: "Settings工具",
                request: {
                    jsonrpc: "2.0",
                    id: 10,
                    method: "tools/call",
                    params: {
                        name: "settings",
                        arguments: {
                            action: "get"
                        }
                    }
                }
            }
        ];

        // 执行测试
        let testIndex = 0;
        function runNextTest() {
            if (testIndex < tests.length) {
                const test = tests[testIndex];
                console.log(`\n${testIndex + 1}. 测试 ${test.name}...`);
                server.stdin.write(JSON.stringify(test.request) + '\n');
                testIndex++;
                setTimeout(runNextTest, 1000);
            } else {
                // 所有测试完成
                setTimeout(() => {
                    server.kill();
                    
                    console.log('\n🎯 全工具测试完成');
                    console.log(`总响应数量: ${responses.length}`);
                    
                    // 分析结果
                    const results = {};
                    tests.forEach(test => {
                        const response = responses.find(r => r.id === test.id);
                        results[test.name] = {
                            success: !!response && !!response.result,
                            response: response
                        };
                    });

                    resolve({
                        success: Object.values(results).filter(r => r.success).length >= 7,
                        results: results,
                        totalResponses: responses.length
                    });
                }, 2000);
            }
        }

        // 开始测试
        setTimeout(runNextTest, 500);

        server.on('error', (error) => {
            console.log(`❌ 服务器错误: ${error.message}`);
            reject(error);
        });
    });
}

// 运行测试
async function runAllToolsTests() {
    try {
        const result = await testAllTools();
        
        console.log('\n🏆 全工具测试总结:');
        console.log(`总体状态: ${result.success ? '✅ 成功' : '❌ 失败'}`);
        console.log(`总响应数量: ${result.totalResponses}`);
        
        console.log('\n📊 各工具测试结果:');
        Object.entries(result.results).forEach(([toolName, result]) => {
            console.log(`${toolName}: ${result.success ? '✅ 成功' : '❌ 失败'}`);
        });
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
    }
}

runAllToolsTests();
