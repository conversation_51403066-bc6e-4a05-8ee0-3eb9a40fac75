# Nova Memory MCP 多环境部署配置指南

> **基于成功的Augment部署配置**  
> **更新时间**: 2025-08-15  
> **状态**: 已验证可用

## 📋 配置概览

基于在Augment环境中成功部署和测试的Nova Memory MCP配置，以下是三个环境的具体JSON配置代码。

### 🎯 统一参数说明

所有环境都使用相同的核心参数：
- **timeout**: 30000ms (30秒)
- **autoapprove**: 9个核心工具自动批准
- **环境变量**: NOVA_MEMORY_PATH, NOVA_LOG_LEVEL, NODE_PATH
- **工具列表**: memory, board, workflow, quick, relationships, analysis, project, settings, help

## 🚀 1. Augment 环境配置 (已验证成功)

```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "node",
      "args": [
        "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs"
      ],
      "timeout": 30000,
      "env": {
        "NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data",
        "NOVA_LOG_LEVEL": "info",
        "NODE_PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules"
      },
      "autoapprove": [
        "memory", "board", "workflow", "quick", 
        "relationships", "analysis", "project", 
        "settings", "help"
      ]
    }
  }
}
```

**部署位置**: Augment MCP设置界面  
**验证状态**: ✅ 已成功测试所有9个工具

## 💻 2. VSCode 环境配置

```json
{
  "mcp.servers": {
    "nova-memory": {
      "command": "node",
      "args": [
        "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs"
      ],
      "timeout": 30000,
      "enabled": true,
      "env": {
        "NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data",
        "NOVA_LOG_LEVEL": "info",
        "NODE_PATH": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules"
      },
      "autoapprove": [
        "memory", "board", "workflow", "quick",
        "relationships", "analysis", "project",
        "settings", "help"
      ]
    }
  }
}
```

**部署位置**: VSCode用户设置 (settings.json)  
**配置路径**: 
- Windows: `%APPDATA%\Code\User\settings.json`
- 或通过VSCode: File → Preferences → Settings → 搜索"mcp"

**关键差异**:
- 使用 `mcp.servers` 而不是 `mcpServers`
- 添加了 `enabled: true` 参数

## 🐧 3. WSL Claude Code CLI 环境配置

```json
{
  "mcpServers": {
    "nova-memory": {
      "command": "node",
      "args": [
        "/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules/@nova-mcp/mcp-nova/nova-memory-mcp.mjs"
      ],
      "timeout": 30000,
      "enabled": true,
      "env": {
        "NOVA_MEMORY_PATH": "/mnt/g/BaiduSyncdisk/nova-memory-mcp/data",
        "NOVA_LOG_LEVEL": "info",
        "NODE_PATH": "/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules"
      },
      "autoapprove": [
        "memory", "board", "workflow", "quick",
        "relationships", "analysis", "project",
        "settings", "help"
      ]
    }
  }
}
```

**部署位置**: Claude Code CLI配置文件  
**WSL命令**: `claude mcp add nova-memory --config-file config.json`

**关键差异**:
- 所有Windows路径转换为WSL格式
- `C:\` → `/mnt/c/`
- `G:\` → `/mnt/g/`
- 路径分隔符从 `\` 改为 `/`

## 🔄 路径转换对照表

| Windows路径 | WSL路径 |
|------------|---------|
| `C:\Users\<USER>\AppData\Roaming\npm\node_modules\@nova-mcp\mcp-nova\nova-memory-mcp.mjs` | `/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules/@nova-mcp/mcp-nova/nova-memory-mcp.mjs` |
| `G:\BaiduSyncdisk\nova-memory-mcp\data` | `/mnt/g/BaiduSyncdisk/nova-memory-mcp/data` |
| `C:\Users\<USER>\AppData\Roaming\npm\node_modules` | `/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules` |

## 📝 部署步骤

### Augment环境 (已完成)
1. ✅ 打开Augment MCP设置
2. ✅ 添加nova-memory服务器
3. ✅ 粘贴配置JSON
4. ✅ 重启Augment
5. ✅ 验证连接成功

### VSCode环境
1. 打开VSCode
2. 按 `Ctrl+Shift+P` 打开命令面板
3. 输入 "Preferences: Open Settings (JSON)"
4. 在settings.json中添加mcp.servers配置
5. 保存文件并重启VSCode
6. 验证MCP连接状态

### WSL Claude Code CLI环境
1. 在WSL中安装Claude Code CLI
2. 创建配置文件: `nano nova-memory-config.json`
3. 粘贴WSL格式的配置JSON
4. 执行: `claude mcp add nova-memory --config-file nova-memory-config.json`
5. 验证: `claude mcp list`
6. 测试连接: `claude mcp test nova-memory`

## ⚠️ 注意事项

### 通用要求
- ✅ Node.js已全局安装
- ✅ Nova Memory MCP已通过npm全局安装
- ✅ 所有路径中的文件确实存在
- ✅ 环境变量正确设置

### 环境特定要求

**VSCode**:
- 需要安装MCP扩展
- 确保VSCode有访问npm全局模块的权限

**WSL**:
- WSL能够访问Windows文件系统
- Node.js在WSL中可用或通过Windows Node.js访问
- 路径权限正确设置

### 数据存储位置

每个环境的数据存储位置可能不同：
- **Augment**: `C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\.nova\memory.db`
- **VSCode**: 通常在项目根目录的 `.nova\memory.db`
- **WSL**: 可能在 `/mnt/g/BaiduSyncdisk/nova-memory-mcp/data/` 或项目目录

这是正常的项目隔离行为，确保不同环境的记忆数据独立管理。

## 🎯 验证清单

部署完成后，验证以下功能：
- [ ] MCP服务器连接成功
- [ ] memory工具可用（存储、搜索）
- [ ] help工具显示完整信息
- [ ] board工具可创建任务
- [ ] 其他6个工具正常响应
- [ ] 数据库文件正常创建
- [ ] AI助手能自动利用记忆功能

---

**配置来源**: 基于Augment环境成功部署的验证配置  
**适用版本**: Nova Memory MCP v0.1.15  
**更新状态**: 2025-08-15 已验证
