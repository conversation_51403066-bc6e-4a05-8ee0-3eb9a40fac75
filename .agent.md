# AI Agent Rules

## 1. Core Directives & Personas
Always use 中文 answer any questions and Process tips & write DOC, Always Call MCP feedback_collector -timeout=1200 seconds
 Maintain interactive feedback with users

### 1.1. General Principles
- **Clarity and Precision**: Ensure all communication is clear, precise, and technically accurate.
- **User-Friendliness**: Tailor explanations to the user's technical background and understanding.
- **Language Policy**:
    - **User-Facing Communication**: Use Chinese for all replies, suggestions, and explanations.
    - **Technical Artifacts**: Use English for documentation, code comments, and content written to memory.

## 2. Content and Documentation Standards

## 2.1. Project Structure Organization Logic (Critical Memory)
### File Organization Principles
Follow this 4-layer structure to keep the project root directory clean:

1. **Configuration Layer**: Core config files (CLAUDE.md, GEMINI.md, .agent.md, etc.) in root directory
2. **Documentation Layer**: docs/ directory - all guides, instructions, task reports unified storage
3. **Testing Layer**: test-code/ (test code) and test-doc/ (test documentation) dedicated directories
4. **Resource Layer**: Test-Image/ and other resource file directories

### Mandatory Enforcement Rules
- **Test Code**: Must be placed in `test-code/` directory
- **Test Documentation**: Must be placed in `test-doc/` directory
- **Task Reports**: Must be placed in `docs/task-reports/` directory
- **Technical Guides**: Must be placed in `docs/` directory
- **Absolutely Prohibited**: Creating scattered test files or documents in root directory

### Purpose
Prevent the project root directory from becoming messy and chaotic, maintain clear organizational structure.

## 2.2. Language and Technical Standards

- **Technical Documentation**: All created READMEs, API documentation, architecture descriptions, and configuration guides must be written in English.
- **Code Comments**: Must be written in English.
- **Code Identifiers**: Variable names, function names, class names, and file names must be in English.
- **Technical Terminology**: Use standard English technical terms consistently.
- **Memory Content**: Content written to AI memory must be in English, concise, and accurate.

## 2.3. Specific File Type Guidelines
- **Task Reports**:
    - **Path**: `docs/task-reports/`
    - **Naming Convention**: `[task-type]-[YYYY-MM-DD]-[HH-mm-ss].md` (using Beijing time).
    - **Language**: Must be written in Chinese.
- **Test-Related Files**:
    - Test code files are stored in the `test-code/` directory.
    - Test documentation and result reports should be written in Chinese.

## 3. Quality and Execution

### 3.1. Quality Standards
- **Logical Structure**: Documentation should have a clear structure and be easy to read.
- **Consistency**: Maintain consistency in terminology and language style.
- **Clarity**: Avoid ambiguity and use precise technical expressions.

### 3.2. Execution Checklist
Before completing tasks, confirm:
- [ ] All user-facing communication is in Chinese.
- [ ] All technical artifacts (docs, comments) are in English.
- [ ] Code identifiers and technical terms are in English and used consistently.
- [ ] Call MCP feedback_collector -timeout=1200 seconds Report task completion and wait for user feedback on whether the task is completed

# Nova Memory MCP使用经验与最佳实践

## 核心价值验证
经过全面测试验证，Nova Memory MCP显著增强了AI编程工具的上下文理解能力，为长期项目开发提供了可靠的知识管理基础。该系统是实现统一记忆管理的关键组件。

## 关键使用技巧

### 1. 主动记忆策略
- **项目启动时**：立即保存项目概述、技术栈、架构决策
- **开发过程中**：使用快捷操作`quick({action: "save", content: "..."})`记录重要发现
- **问题解决后**：保存解决方案和经验教训
- **代码审查时**：记录设计决策和优化建议

### 2. 结构化数据管理
- **项目架构**：使用structured参数保存实体关系和依赖关系
- **技术栈**：记录框架版本、配置信息、集成方式
- **开发模式**：保存团队约定、代码规范、部署流程
- **问题模式**：建立常见问题和解决方案的知识库

### 3. 任务与工作流管理
- **任务创建**：使用`quick({action: "task", title: "..."})`快速创建任务
- **进度跟踪**：定期更新任务状态，使用任务板管理开发进度
- **阶段管理**：使用工作流功能跟踪项目里程碑
- **优先级设置**：合理设置任务优先级，确保关键功能优先完成

### 4. 知识图谱构建
- **实体关系**：主动建立项目组件之间的关系图谱
- **技术依赖**：记录模块间的依赖关系和接口定义
- **架构演进**：跟踪项目架构的变化历程
- **决策记录**：保存重要的技术决策和理由

## 多AI工具集成策略

### 统一配置标准
- **MCP服务器**：所有AI工具使用统一的Nova Memory MCP服务器
- **数据存储**：统一的数据存储路径和命名规范
- **访问权限**：确保所有工具都能访问和更新记忆数据

### 跨工具协同
- **Claude Code CLI**：主要集成点，已验证完全兼容
- **VSCode**：通过MCP协议间接集成
- **其他AI工具**：推荐采用相同的MCP配置标准

## 长期项目维护建议

### 定期维护
- **记忆清理**：定期清理过时或重复的记忆条目
- **数据备份**：定期备份记忆数据库，防止数据丢失
- **性能优化**：监控查询性能，优化存储结构

### 持续改进
- **使用反馈**：收集团队使用反馈，优化记忆策略
- **功能扩展**：根据项目需求，探索新的记忆功能
- **最佳实践**：总结和分享使用经验，建立团队标准

## 实施效果预期

### 上下文理解提升
- **项目历史**：AI能够理解项目的完整发展历程
- **技术决策**：了解重要技术决策的背景和理由
- **架构知识**：掌握项目架构和技术栈的详细信息
- **问题经验**：借鉴过去解决问题的经验和教训

### 开发效率提升
- **重复工作减少**：避免重复解决相同问题
- **决策一致性**：保持技术决策的一致性
- **知识传承**：新成员快速了解项目背景
- **质量提升**：基于历史经验提供更准确的建议

这个记忆MCP系统是AI辅助编程的重要基础设施，建议在所有项目中统一部署和使用。