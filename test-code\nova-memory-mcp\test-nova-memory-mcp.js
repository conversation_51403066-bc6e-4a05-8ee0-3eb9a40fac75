#!/usr/bin/env node

// Nova Memory MCP 功能测试脚本
const { spawn } = require('child_process');
const path = require('path');

// 设置环境变量
process.env.NOVA_MEMORY_PATH = 'G:\\BaiduSyncdisk\\nova-memory-mcp\\data';
process.env.NOVA_LOG_LEVEL = 'info';

console.log('🧠 Nova Memory MCP 功能测试');
console.log('================================');

// MCP服务器路径
const mcpServerPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs';

console.log(`📍 MCP服务器路径: ${mcpServerPath}`);
console.log(`📁 数据存储路径: ${process.env.NOVA_MEMORY_PATH}`);
console.log(`📊 日志级别: ${process.env.NOVA_LOG_LEVEL}`);

// 测试MCP服务器启动
function testMCPServer() {
    return new Promise((resolve, reject) => {
        console.log('\n🚀 启动MCP服务器测试...');
        
        const server = spawn('node', [mcpServerPath], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: process.env
        });

        let output = '';
        let errorOutput = '';

        server.stdout.on('data', (data) => {
            output += data.toString();
        });

        server.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        // 发送初始化请求
        setTimeout(() => {
            const initRequest = {
                jsonrpc: "2.0",
                id: 1,
                method: "initialize",
                params: {
                    protocolVersion: "2024-11-05",
                    capabilities: {
                        tools: {}
                    },
                    clientInfo: {
                        name: "test-client",
                        version: "1.0.0"
                    }
                }
            };

            server.stdin.write(JSON.stringify(initRequest) + '\n');
        }, 1000);

        // 测试memory工具
        setTimeout(() => {
            const memoryRequest = {
                jsonrpc: "2.0",
                id: 2,
                method: "tools/call",
                params: {
                    name: "memory",
                    arguments: {
                        action: "store",
                        content: "测试记忆内容：Nova Memory MCP部署成功，使用混合安装方式，数据存储在G盘自定义目录",
                        tags: ["测试", "部署", "配置"],
                        metadata: {
                            project: "nova-memory-test",
                            timestamp: new Date().toISOString()
                        }
                    }
                }
            };

            server.stdin.write(JSON.stringify(memoryRequest) + '\n');
        }, 2000);

        // 等待响应
        setTimeout(() => {
            server.kill();
            
            console.log('\n📤 服务器输出:');
            console.log(output || '(无输出)');
            
            if (errorOutput) {
                console.log('\n❌ 错误输出:');
                console.log(errorOutput);
            }

            resolve({
                success: !errorOutput,
                output: output,
                error: errorOutput
            });
        }, 5000);

        server.on('error', (error) => {
            console.log(`❌ 服务器启动失败: ${error.message}`);
            reject(error);
        });
    });
}

// 检查数据文件
function checkDataFiles() {
    const fs = require('fs');
    const dataPath = process.env.NOVA_MEMORY_PATH;
    
    console.log('\n📁 检查数据文件...');
    
    try {
        const files = fs.readdirSync(dataPath);
        console.log(`✅ 数据目录存在: ${dataPath}`);
        
        if (files.length > 0) {
            console.log('📄 数据文件:');
            files.forEach(file => {
                const filePath = path.join(dataPath, file);
                const stats = fs.statSync(filePath);
                console.log(`  - ${file} (${stats.size} bytes)`);
            });
        } else {
            console.log('📭 数据目录为空（首次运行正常）');
        }
    } catch (error) {
        console.log(`❌ 无法访问数据目录: ${error.message}`);
    }
}

// 运行测试
async function runTests() {
    try {
        // 检查数据文件
        checkDataFiles();
        
        // 测试MCP服务器
        const result = await testMCPServer();
        
        console.log('\n🎯 测试结果总结:');
        console.log(`✅ MCP服务器启动: ${result.success ? '成功' : '失败'}`);
        
        // 再次检查数据文件
        checkDataFiles();
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
    }
}

// 启动测试
runTests();
