{"mcpServers": {"nova-memory": {"command": "node", "args": ["/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules/@nova-mcp/mcp-nova/nova-memory-mcp.mjs"], "timeout": 30000, "enabled": true, "env": {"NOVA_MEMORY_PATH": "/mnt/g/BaiduSyncdisk/nova-memory-mcp/data", "NOVA_LOG_LEVEL": "info", "NODE_PATH": "/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules"}, "autoapprove": ["memory", "board", "workflow", "quick", "relationships", "analysis", "project", "settings", "help"]}}}